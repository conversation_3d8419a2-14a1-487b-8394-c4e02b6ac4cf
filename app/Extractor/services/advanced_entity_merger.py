"""
Advanced Entity Merger for combining multiple AI extraction results

This module provides sophisticated merging logic with confidence scoring,
conflict resolution, and data validation for entity extraction results.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import defaultdict, Counter
from difflib import SequenceMatcher


class AdvancedEntityMerger:
    """
    Advanced merger for combining multiple AI extraction results with confidence scoring
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

        # Field configuration
        self.list_fields = {
            "business_email", "support_email", "business_contact_numbers",
            "business_location", "shipping_countries", "jurisdiction_place"
        }

        self.boolean_fields = {
            "accepts_international_orders", "has_jurisdiction_law"
        }

        self.string_fields = {
            "legal_name", "shipping_policy_details", "jurisdiction_details"
        }

        # Source priority (higher number = higher priority)
        self.source_priority = {
            "gemini": 1,
            "openai": 1
        }

    def merge_multiple_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Merge multiple AI extraction results using advanced strategies

        Args:
            results: List of results with format:
                    [{"data": {...}, "source": "gemini|openai", "source_index": int}]

        Returns:
            Dict containing merged results
        """
        if not results:
            return {}

        self.logger.info(f"Merging {len(results)} AI extraction results")

        # Group results by field
        field_values = defaultdict(list)

        for result in results:
            data = result.get("data", {})
            source = result.get("source", "unknown")
            source_index = result.get("source_index", 0)

            for field, value in data.items():
                if field == "error":
                    continue

                # Skip garbage values
                if self._is_garbage_value(value):
                    continue

                field_values[field].append({
                    "value": value,
                    "source": source,
                    "source_index": source_index,
                    "priority": self.source_priority.get(source, 0)
                })

        # Merge each field using appropriate strategy
        merged_result = {}
        for field, value_list in field_values.items():
            merged_value = self._merge_field_values(field, value_list)
            if merged_value is not None:
                merged_result[field] = merged_value

        self.logger.info(f"Merged result contains {len(merged_result)} fields")
        return merged_result

    def _merge_field_values(self, field: str, value_list: List[Dict[str, Any]]) -> Any:
        """
        Merge values for a specific field using appropriate strategy
        """
        if not value_list:
            return None

        # Sort by priority (highest first), then by source index
        value_list.sort(key=lambda x: (x["priority"], -x["source_index"]), reverse=True)

        if field in self.list_fields:
            return self._merge_list_field(field, value_list)
        elif field in self.boolean_fields:
            return self._merge_boolean_field(field, value_list)
        elif field in self.string_fields:
            return self._merge_string_field(field, value_list)
        else:
            # Default: return highest priority value
            return value_list[0]["value"]

    def _merge_list_field(self, field: str, value_list: List[Dict[str, Any]]) -> List[str]:
        """
        Merge list fields with intelligent deduplication
        """
        all_items = []
        seen_items = set()

        for value_data in value_list:
            value = value_data["value"]
            priority = value_data["priority"]

            if isinstance(value, list):
                for item in value:
                    if item and str(item).strip():
                        normalized_item = str(item).strip().lower()
                        if normalized_item not in seen_items:
                            all_items.append({
                                "item": str(item).strip(),
                                "priority": priority,
                                "normalized": normalized_item
                            })
                            seen_items.add(normalized_item)

        # Sort by priority and return items
        all_items.sort(key=lambda x: x["priority"], reverse=True)
        return [item["item"] for item in all_items]

    def _merge_boolean_field(self, field: str, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge boolean fields using voting with priority weighting
        """
        votes = {"yes": 0, "no": 0, "not_mentioned": 0}

        for value_data in value_list:
            value = str(value_data["value"]).lower().strip()
            priority = value_data["priority"]

            if value in ["yes", "true", "1"]:
                votes["yes"] += priority
            elif value in ["no", "false", "0"]:
                votes["no"] += priority
            else:
                votes["not_mentioned"] += priority

        # Special case for jurisdiction: "yes" should win if there's any "yes" vote
        if field == "has_jurisdiction_law" and votes["yes"] > 0:
            return "yes"

        # Return the option with highest weighted vote
        return max(votes.items(), key=lambda x: x[1])[0]

    def _merge_string_field(self, field: str, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge string fields using priority-based selection
        """
        # For legal name, use special merging logic
        if field == "legal_name":
            return self._merge_legal_name(value_list)

        # Default: return highest priority non-empty value
        for value_data in value_list:
            value = value_data["value"]
            if value and str(value).strip():
                return str(value).strip()

        return ""

    def _merge_legal_name(self, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge legal name with similarity checking
        """
        names = []
        for value_data in value_list:
            value = value_data["value"]
            priority = value_data["priority"]
            if value and str(value).strip():
                names.append({
                    "name": str(value).strip(),
                    "priority": priority
                })

        if not names:
            return ""

        # If only one name, return it
        if len(names) == 1:
            return names[0]["name"]

        # Check for similar names and merge
        best_name = names[0]["name"]
        best_priority = names[0]["priority"]

        for name_data in names[1:]:
            name = name_data["name"]
            priority = name_data["priority"]

            # Check similarity
            similarity = SequenceMatcher(None, best_name.lower(), name.lower()).ratio()

            if similarity > 0.8:  # Very similar names
                # Keep the one with higher priority
                if priority > best_priority:
                    best_name = name
                    best_priority = priority
            elif priority > best_priority:  # Higher priority
                best_name = name
                best_priority = priority

        return best_name



    def _is_garbage_value(self, value: Any) -> bool:
        """
        Enhanced garbage value detection with better pattern matching
        """
        if value is None:
            return True

        if isinstance(value, str):
            # Empty or whitespace-only strings
            if not value.strip():
                return True

            # Enhanced garbage patterns
            garbage_patterns = [
                r'^(not?[_\s]?(applicable|available|found|specified|mentioned|provided|stated|given|disclosed|indicated|clear))',
                r'^(n/?a|na|none|null|undefined|unknown|unclear)$',
                r'^(information[_\s]?not[_\s]?available)',
                r'^(details[_\s]?not[_\s]?provided)',
                r'^(not[_\s]?extractable|cannot[_\s]?extract|unable[_\s]?to[_\s]?extract)',
                r'^(extraction[_\s]?failed|no[_\s]?extraction[_\s]?possible)',
                r'^(no[_\s]?(information|data|details|mention))',
                r'^[\-\*\.\s]+$',  # Only punctuation and spaces
            ]

            cleaned_value = value.lower().strip()
            for pattern in garbage_patterns:
                if re.match(pattern, cleaned_value):
                    return True

            return False

        if isinstance(value, list):
            # Empty lists are garbage
            if not value:
                return True
            # Lists containing only garbage strings are garbage
            return all(self._is_garbage_value(item) for item in value)

        if isinstance(value, dict):
            # Empty dicts are garbage
            if not value:
                return True
            # Dicts with only garbage values are garbage
            return all(self._is_garbage_value(v) for v in value.values())

        # For other types (int, float, bool), only None is garbage
        return False
