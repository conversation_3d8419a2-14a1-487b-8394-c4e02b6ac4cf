"""
Entity Extractor Orchestrator Service

Main orchestrator that coordinates all entity extraction and analysis services
"""

import json
from datetime import datetime
from typing import Dict, Optional, Any
from sqlmodel import Session
import re
from app.database import get_session, engine
from app.models.db_models import EntityExtractionAnalysis, EntityExtractionUrlAnalysis
from app.Extractor.models.request_models import EntityExtractionRequest, EntityExtractionResponse
from app.Extractor.utils.logger import EntityExtractorLogger
from app.Extractor.services.entity_extraction_service import EntityExtractionService
from app.Extractor.services.url_data_retrieval import UrlDataRetrievalService
from app.gpt_models.chatgpt_utils import call_gpt4o_mini
from app.Extractor.utils.text_cleaner import clean_policy_text
from app.gpt_models.gpt_logger import api_response_logger
from app.Extractor.utils.api_response_logger import api_logger
from app.Extractor.services.advanced_entity_merger import AdvancedEntityMerger


def _normalize_for_db(value: Any) -> Optional[str]:
        if value is None:
            return ""
        if isinstance(value, str):
            if value.strip() == "":
                return ""
            if value.lower().strip() == "null":
                return ""
        return value
def string_to_list(value: str) -> list[str]:
        if not value:
            return []
        import re
        return [item.strip() for item in re.split(r'[,;\n|]', value) if item.strip()]
class EntityExtractorOrchestrator:
    """
    Main orchestrator for entity extraction and policy analysis
    """
    
    def __init__(self):
        self.logger = None  # Will be initialized per request
    def clean_scraped_text(self, text_content: str) -> str:
        if not text_content:
            return ""
        text = text_content

        noise_patterns = [
            r'\b(search|cart|menu|login|logout|subscribe|newsletter|offers|discount|popular searches)\b',
            r'(©|®|™|all rights reserved|powered by|privacy policy|terms & conditions)',
            r'[\r\n]+',
            r'\s{2,}',
            r'(https?://\S+)',
            r'[^\x00-\x7F]+',
        ]
        for pattern in noise_patterns:
            text = re.sub(pattern, ' ', text, flags=re.IGNORECASE)
        text = re.sub(r'\s+', ' ', text).strip()
        fragments = text.split('. ')
        filtered = [f for f in fragments if len(f) > 5]
        return '. '.join(filtered)

    def extract_relevant_sections(self, text: str, keywords: list[str]) -> str:
        if not text:
            return ""
        paragraphs = text.split('. ')
        keywords_lower = [kw.lower() for kw in keywords]
        relevant = [p for p in paragraphs if any(kw in p.lower() for kw in keywords_lower)]
        return '. '.join(relevant)

    def extract_emails(self, text: str) -> list[str]:
        email_pattern = r'[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+'
        return list(set(re.findall(email_pattern, text)))

    def extract_phone_numbers(self, text: str) -> list[str]:
        phone_pattern = r'\+?\d[\d\s\-]{7,}\d'
        phones = re.findall(phone_pattern, text)
        cleaned = set(p.strip().replace(' ', '').replace('-', '') for p in phones if len(p.strip()) >= 7)
        return list(cleaned)    
    def process_entity_extraction(self, request: EntityExtractionRequest) -> EntityExtractionResponse:
            """
            Process main policy URLs, route to Gemini first, then fallback to OpenAI for any missing fields or unreachable URLs, merge results, and return.
            """
            self.logger = EntityExtractorLogger(
                analysis_id=f"orchestrator_{request.scrape_request_ref_id}",
                scrape_request_id=request.scrape_request_ref_id
            )
            try:
                self.logger.info("Starting simplified entity extraction orchestration", {
                    "scrape_request_ref_id": request.scrape_request_ref_id,
                    "website_url": request.website_url,
                    "org_id": request.org_id
                })

                # Create or get existing analysis record
                existing_analysis = self._get_existing_analysis(request.scrape_request_ref_id, request.org_id)
                if existing_analysis and not request.force_reprocess:
                    self.logger.info(f"Returning existing analysis: {existing_analysis.id}")
                    return self._convert_to_response(existing_analysis)

                # Create new analysis record
                analysis = self._create_analysis_record(request)
                
                # Update logger with real analysis ID
                self.logger.analysis_id = analysis.id
                self.logger.info(f"Updated logger with analysis ID: {analysis.id}")
                
                self._update_analysis_status(analysis.id, "IN_PROGRESS", started_at=datetime.now().isoformat())

                # 1. Get the main policy URLs
                url_retrieval_service = UrlDataRetrievalService(request.scrape_request_ref_id, request.org_id)
                policy_urls_data = url_retrieval_service.get_policy_urls_with_reachability(request.website_url)
                main_policy_types = [
                    "privacy_policy", "terms_and_condition", "returns_exchange_policy", "contact_us", "home_page", "about_us", "shipping_delivery"
                ]
                filtered_policy_urls = {k: v for k, v in policy_urls_data.items() if k in main_policy_types}
                # ADD DEBUG LOGGING HERE
                self.logger.info(f"Filtered policy URLs keys: {list(filtered_policy_urls.keys())}")
                for key in ['privacy_policy', 'terms_and_condition']:
                    if key in filtered_policy_urls:
                        length = len(filtered_policy_urls[key].get('extracted_text') or "")
                        self.logger.info(f"Extracted text length for {key}: {length}")
                    else:
                        self.logger.info(f"Key {key} missing in filtered_policy_urls")
                if not filtered_policy_urls:
                    # Store failed analysis in database
                    analysis_id = self._store_failed_analysis(request, "No main policy URLs found.")

                    return EntityExtractionResponse(
                        analysis_id=analysis_id,
                        scrape_request_ref_id=request.scrape_request_ref_id,
                        website_url=request.website_url,
                        processing_status="FAILED",
                        error_message="No main policy URLs found.",
                        created_at=datetime.now().isoformat()
                    )

                # 2. Check Gemini reachability
                urls = [v.get('url') or v.get('original_url') for v in filtered_policy_urls.values() if v.get('url') or v.get('original_url')]
                reachable_urls, unreachable_urls = url_retrieval_service.check_gemini_reachability(urls)

                # 3. Gemini extraction for reachable URLs
                gemini_result = {}
                gemini_failed = False
                if reachable_urls:
                    from app.Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
                    # Only pass reachable URLs of the main types
                    reachable_policy_urls = {k: v.get('url') or v.get('original_url') for k, v in filtered_policy_urls.items() if (v.get('url') or v.get('original_url')) in reachable_urls}
                    # Build prompt: instruct to analyze first and last 10000 words, extract shipment/international shipping
                    prompt = EntityExtractionPrompts.get_entity_extraction_prompt(reachable_policy_urls)
                    prompt += "\n\nIMPORTANT: For each policy, analyze only the first 10000 words and the last 10000 words. Also, extract whether shipment/international shipping is accepted (yes/no)."
                    from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_legacy
                    gemini_response = get_gemini_response_legacy(prompt, model_name="gemini-2.5-flash")
                    
                    # Log Gemini API response
                    api_logger.log_gemini_response(
                        prompt=prompt,
                        response=gemini_response,
                        model_name="gemini-2.5-flash",
                        request_id=request.scrape_request_ref_id,
                        context={"service": "orchestrator", "urls": list(reachable_policy_urls.keys())}
                    )

                    # Check if Gemini failed (returns error string)
                    if isinstance(gemini_response, str) and ("error" in gemini_response.lower() or "max_tokens" in gemini_response.lower() or "finish reason" in gemini_response.lower()):
                        self.logger.warning(f"Gemini failed after all retries: {gemini_response}")
                        gemini_failed = True
                        gemini_result = {"error": f"Gemini failed: {gemini_response}"}
                    else:
                        # Parse JSON from Gemini response
                        import json
                        try:
                            if "```json" in gemini_response:
                                json_start = gemini_response.find("```json") + 7
                                json_end = gemini_response.find("```", json_start)
                                json_str = gemini_response[json_start:json_end].strip()
                            else:
                                json_str = gemini_response.strip()
                            gemini_result = json.loads(json_str)
                            
                            # NOTE: Gemini response already logged by api_logger.log_gemini_response() above
                            # Removed duplicate api_response_logger.log_response() call to prevent multiple JSON files
                        except Exception as e:
                            self.logger.warning(f"Gemini response parse error: {str(e)}")
                            gemini_failed = True
                            gemini_result = {"error": f"Gemini response parse error: {str(e)}", "raw": gemini_response}

                # 3a. Build list of any fields Gemini left null
                expected_fields = [
                    "legal_name",
                    "business_email",
                    "support_email",
                    "business_contact_numbers",
                    "business_location",
                    "accepts_international_orders",
                    "shipping_countries",
                    "shipping_policy_details",
                    "has_jurisdiction_law",
                    "jurisdiction_place",
                    "jurisdiction_details"
                ]
                # fields with None, empty list/string, or missing entirely
                # Use a proper function to check if values are actually missing/empty
                def is_field_missing_or_empty(field_value):
                    if field_value is None:
                        return True
                    if isinstance(field_value, str) and field_value.strip() == "":
                        return True
                    if isinstance(field_value, list) and (len(field_value) == 0 or all(not item or str(item).strip() == "" for item in field_value)):
                        return True
                    if isinstance(field_value, dict) and len(field_value) == 0:
                        return True
                    return False
                
                missing_fields = [
                    f for f in expected_fields
                    if is_field_missing_or_empty(gemini_result.get(f))
                ]

                # Mapping from field → which policy URL to use
                field_policy_map = {
                    # contact page for all contact-related fields
                    "business_email": "contact_us",
                    "support_email": "contact_us",
                    "business_contact_numbers": "contact_us",
                    # shipping policy page
                    "accepts_international_orders": "shipping_delivery",
                    "shipping_countries": "shipping_delivery",
                    "shipping_policy_details": "shipping_delivery",
                    # terms & conditions page for jurisdiction
                    "has_jurisdiction_law": "terms_and_condition",
                    "jurisdiction_place": "terms_and_condition",
                    "jurisdiction_details": "terms_and_condition",
                    # home/about for company identity
                    "legal_name": "home_page",
                    "business_location": "about_us",
                    # "privacy_policy_text": "privacy_policy",
                    # "terms_conditions_text": "terms_and_condition"
                }
                self.logger.info(f"Gemini missing fields to fallback: {missing_fields}")

                # 4. Enhanced OpenAI fallback for any missing field using policy_analysis_new_gemini text
                openai_results = []
                if missing_fields:
                    self.logger.info(f"Starting OpenAI backup flow for missing fields: {missing_fields}")

                    # Get all available extracted text from policy_analysis_new_gemini table
                    policy_texts = self._get_policy_texts_from_db(request.scrape_request_ref_id)

                    if policy_texts:
                        self.logger.info(f"Found policy texts for {len(policy_texts)} policy types")

                        # Process each missing field with OpenAI using extracted text
                        openai_backup_result = self._process_missing_fields_with_openai(
                            missing_fields,
                            policy_texts,
                            request.scrape_request_ref_id
                        )

                        if openai_backup_result:
                            openai_results.append(openai_backup_result)
                            self.logger.info(f"OpenAI backup extracted: {list(openai_backup_result.keys())}")
                    else:
                        self.logger.warning("No policy texts found in database for OpenAI backup")

                    # Fallback to original logic if no policy texts found
                    if not policy_texts:
                        from app.Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
                        keywords_map = {
                            "contact_us": ["email", "contact", "phone", "support"],
                            "shipping_delivery": ["shipping", "delivery", "international", "return", "cancel"],
                            "terms_and_condition": ["jurisdiction", "law", "dispute", "governed"],
                            "about_us": [],
                            "home_page": []
                        }
                        for field in missing_fields:
                            policy_type = field_policy_map.get(field)
                            url_data = filtered_policy_urls.get(policy_type)
                            if not url_data:
                                continue
                            url = url_data.get("url") or url_data.get("original_url")
                            # only if that URL was reachable by Gemini
                            if url in reachable_urls:
                                # get the scraped text from policy_analysis_new_gemini
                                text_content = url_data.get("extracted_text") or url_retrieval_service.get_extracted_text_for_url(url)
                                if not text_content:
                                    continue

                            # prepare prompt: ask only for the single missing field
                            processed_text = self.clean_scraped_text(text_content)
                            keywords = keywords_map.get(policy_type, [])
                            focused_text = self.extract_relevant_sections(processed_text, keywords) if keywords else processed_text

                            prompt = EntityExtractionPrompts.get_entity_extraction_text_prompt(url, focused_text, policy_type)
                            
                            # prompt += (
                            #     f"\n\nCRITICAL: This request is to recover *only* the \"{field}\" field. "
                            #     "Return a JSON object with just that key and its value."
                            # )

                            messages = [{"role": "user", "content": prompt}]
                            openai_resp = call_gpt4o_mini(messages, name="entity_extraction")
                            
                            # Log OpenAI API response
                            api_logger.log_openai_response(
                                messages=messages,
                                response=openai_resp,
                                name="entity_extraction",
                                request_id=request.scrape_request_ref_id,
                                context={"service": "orchestrator", "missing_field": field}
                            )
                            if openai_resp:
                                raw_content = openai_resp.choices[0].message.content

                                # Strip markdown code fences if present
                                import re
                                json_text_match = re.search(r"```json\s*(\{.*?\})\s*```", raw_content, re.DOTALL)
                                if json_text_match:
                                    json_text = json_text_match.group(1)
                                else:
                                    # fallback to raw_content if no code fences
                                    json_text = raw_content.strip()

                                try:
                                    # Add debug logging before parsing
                                    self.logger.info(f"About to parse JSON: {json_text}")
                                    parsed = json.loads(json_text)
                                    
                                    # Add debug logging after parsing
                                    self.logger.info(f"Successfully parsed JSON with keys: {list(parsed.keys())}")
                                    if "jurisdiction_details" in parsed:
                                        self.logger.info(f"Found jurisdiction_details in parsed result: {parsed['jurisdiction_details']}")
                                    if "jurisdiction_place" in parsed:
                                        self.logger.info(f"Found jurisdiction_place in parsed result: {parsed['jurisdiction_place']}")
                                    
                                except Exception as e:
                                    self.logger.error(f"OpenAI JSON parsing error for field '{field}': {str(e)}. Raw response: {raw_content}")
                                    parsed = {}

                                # Supplement emails and phones if missing
                                if (field in ["business_email", "support_email"]) and not parsed.get(field):
                                    parsed[field] = self.extract_emails(focused_text)
                                if field == "business_contact_numbers" and not parsed.get(field):
                                    parsed[field] = self.extract_phone_numbers(focused_text)

                                openai_results.append(parsed)
                            else:
                                self.logger.info(f"No OpenAI response for field '{field}' fallback")
                else:
                    self.logger.info("No missing fields detected; skipping inline OpenAI fallback")
                # 5. Now also process completely unreachable URLs as before
                urls_to_process_with_openai = list(unreachable_urls)

                # If Gemini failed, add reachable URLs to OpenAI processing
                if gemini_failed and reachable_urls:
                    self.logger.info(f"Gemini failed, falling back to OpenAI for {len(reachable_urls)} reachable URLs")
                    urls_to_process_with_openai.extend(reachable_urls)
                    # Reset gemini_result since we're falling back completely
                    gemini_result = {}

                if urls_to_process_with_openai:
                    from app.Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
                    
                    for policy_type, v in filtered_policy_urls.items():
                        url = v.get('url') or v.get('original_url')
                        if url in urls_to_process_with_openai:
                            # Get text from policy analysis tables only
                            text_content = v.get('extracted_text') or url_retrieval_service.get_extracted_text_for_url(url)
                            
                            if text_content:
                                # Apply text splitting logic as per diagram
                                processed_text = self._process_text_for_openai(text_content)
                                keywords = keywords_map.get(policy_type, [])
                                focused_text = self.extract_relevant_sections(processed_text, keywords) if keywords else processed_text

                                prompt = EntityExtractionPrompts.get_entity_extraction_text_prompt(url, focused_text, policy_type)


                                # # Add specific instructions based on policy type
                                # if policy_type == "privacy_policy":
                                #     prompt += "\n\nCRITICAL INSTRUCTION: This text is from a PRIVACY POLICY page. You MUST return the COMPLETE input text exactly as provided in the 'privacy_policy_text' field. DO NOT return null for this field."
                                # elif policy_type == "terms_and_condition":
                                #     prompt += "\n\nCRITICAL INSTRUCTION: This text is from a TERMS & CONDITIONS page. You MUST return the COMPLETE input text exactly as provided in the 'terms_conditions_text' field. DO NOT return null for this field."

                                # prompt += "\n\nOnly use the provided text (from policy analysis tables). Extract shipment/international shipping yes/no as well."

                                # Prepare messages for OpenAI
                                messages = [{"role": "user", "content": prompt}]
                                openai_response_obj = call_gpt4o_mini(messages, name="entity_extraction")
                                
                                # Log OpenAI API response
                                api_logger.log_openai_response(
                                    messages=messages,
                                    response=openai_response_obj,
                                    name="entity_extraction",
                                    request_id=request.scrape_request_ref_id,
                                    context={"service": "orchestrator", "url": url}
                                )
                                openai_response = openai_response_obj.choices[0].message.content if openai_response_obj else None

                                if openai_response:
                                    try:
                                        openai_result = json.loads(openai_response)
                                        if not openai_result.get("business_email"):
                                            openai_result["business_email"] = self.extract_emails(focused_text)
                                        if not openai_result.get("business_contact_numbers"):
                                            openai_result["business_contact_numbers"] = self.extract_phone_numbers(focused_text)
                                        
                                        # Log successful OpenAI response
                                        api_response_logger.log_response(
                                            "openai_entity_extraction",
                                            {"prompt": prompt, "focused_text_length": len(focused_text)},
                                            {"raw_response": openai_response, "parsed_result": openai_result},
                                            {"scrape_request_ref_id": request.scrape_request_ref_id, "website_url": request.website_url}
                                        )
                                    except Exception as e:
                                        openai_result = {"error": f"OpenAI response parse error: {str(e)}", "raw": openai_response}
                                    openai_results.append(openai_result)
                                else:
                                    openai_results.append({"error": "No response from OpenAI", "url": url})
                # 6. Merge results intelligently
                merged_result = self._merge_extraction_results(gemini_result, openai_results)
                # 6a. If OpenAI returned nothing (or a garbage token) for our policy pages,
                #      fall back to the raw text we already pulled from the policy tables.
                self.logger.info(f"Final merged extraction result keys: {list(merged_result.keys())}")
                for field in ["business_email", "business_contact_numbers"]:
                    if not merged_result.get(field):
                        raw_text = filtered_policy_urls.get(field_policy_map[field], {}).get("extracted_text")
                        if raw_text:
                            if field == "business_email":
                                merged_result[field] = self.extract_emails(raw_text)
                            else:
                                merged_result[field] = self.extract_phone_numbers(raw_text)
                for policy_type, json_key in [
                    ("privacy_policy",     "privacy_policy_text"),
                    ("terms_and_condition","terms_conditions_text")]:
                    if not merged_result.get(json_key):
                        original = filtered_policy_urls.get(policy_type, {}).get("extracted_text")
                        self.logger.info(f"Fallback raw text for {json_key}: {'present' if original else 'missing'}")
                        if original:
                            merged_result[json_key] = original
                        self.logger.info(f"After fallback, merged_result[{json_key}] length: {len(merged_result.get(json_key) or '')}")
                            
                # 6b. Clean only the final merged policy texts
                
                # for json_key in ("privacy_policy_text", "terms_conditions_text"):
                #     text = merged_result.get(json_key)
                #     if text:
                #         merged_result[json_key] = clean_policy_text(text)

                # 7. Store results in database
                analysis_id = self._store_analysis_results(
                    request=request,
                    merged_result=merged_result,
                    reachable_urls=reachable_urls,
                    unreachable_urls=unreachable_urls,
                    filtered_policy_urls=filtered_policy_urls,
                    extraction_method="mixed" if gemini_result and openai_results else ("gemini" if gemini_result else "openai")
                )

                # Create proper response with all required fields (convert lists to strings for Pydantic validation)
                self.logger.info(f"Creating response with merged_result keys: {list(merged_result.keys())}")
                self.logger.info(f"Response jurisdiction_details: {merged_result.get('jurisdiction_details')}")
                self.logger.info(f"Response jurisdiction_place: {merged_result.get('jurisdiction_place')}")
                self.logger.info(f"Response has_jurisdiction_law: {merged_result.get('has_jurisdiction_law')}")
                
                # Convert list fields to strings for API response (Pydantic expects strings)
                list_fields = ["business_email", "support_email", "business_contact_numbers", "business_location", "shipping_countries", "jurisdiction_place"]
                response_values = {}
                for field in list_fields:
                    value = merged_result.get(field)
                    if isinstance(value, list):
                        response_values[field] = self._list_to_string(value)
                    else:
                        response_values[field] = value
                
                return EntityExtractionResponse(
                    analysis_id=analysis_id,
                    scrape_request_ref_id=request.scrape_request_ref_id,
                    website_url=request.website_url,
                    processing_status="COMPLETED",
                    legal_name=merged_result.get("legal_name"),
                    business_email=response_values.get("business_email"),
                    support_email=response_values.get("support_email"),
                    business_contact_numbers=response_values.get("business_contact_numbers"),
                    business_location=response_values.get("business_location"),
                    has_jurisdiction_law=merged_result.get("has_jurisdiction_law"),  # Keep as string: "yes"/"no"/"unclear"
                    jurisdiction_details=merged_result.get("jurisdiction_details"),
                    jurisdiction_place=response_values.get("jurisdiction_place"),
                    shipping_countries=response_values.get("shipping_countries"),
                    accepts_international_orders=merged_result.get("accepts_international_orders"),  # Keep as string: "yes"/"no"/"unclear"
                    shipping_policy_details=merged_result.get("shipping_policy_details"),
                    extraction_method="mixed" if gemini_result and openai_results else ("gemini" if gemini_result else "openai"),
                    urls_processed=list(filtered_policy_urls.keys()),
                    error_message=merged_result.get("error"),
                    created_at=datetime.now().isoformat()
                )
            except Exception as e:
                self.logger.error(f"Entity extraction failed: {str(e)}")

                # Store failed analysis in database
                analysis_id = self._store_failed_analysis(request, str(e))

                return EntityExtractionResponse(
                    analysis_id=analysis_id,
                    scrape_request_ref_id=request.scrape_request_ref_id,
                    website_url=request.website_url,
                    processing_status="FAILED",
                    error_message=f"Extraction error: {str(e)}",
                    created_at=datetime.now().isoformat()
            )

    def _merge_extraction_results(self, gemini_result: Dict[str, Any], openai_results: list[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Advanced intelligent merging of multiple AI extraction results with confidence scoring
        """
        # Initialize the advanced merger
        merger = AdvancedEntityMerger(logger=self.logger)

        # Prepare all results for processing
        all_results = []

        # Add Gemini result with source info
        if gemini_result and not gemini_result.get("error"):
            all_results.append({
                "data": gemini_result,
                "source": "gemini",
                "source_index": 0
            })

        # Add OpenAI results with source info
        for idx, openai_result in enumerate(openai_results):
            if not openai_result.get("error"):
                all_results.append({
                    "data": openai_result,
                    "source": "openai",
                    "source_index": idx
                })

        # Perform advanced merging
        merged_result = merger.merge_multiple_results(all_results)

        return merged_result

    def _is_garbage_value(self, value) -> bool:
        """
        Check if a value should be considered garbage/empty
        Uses the enhanced garbage detection from AdvancedEntityMerger
        """
        # Use the advanced merger's garbage detection for consistency
        merger = AdvancedEntityMerger(logger=self.logger)
        return merger._is_garbage_value(value)

    def _normalize_to_list(self, value: Any) -> list[str]:
        """
        Convert various input formats to a list of strings

        Args:
            value: Input value (string, list, or other)

        Returns:
            List of non-empty strings
        """
        if value is None or value == "" or value == "null":
            return []

        if isinstance(value, list):
            # Already a list, filter out empty values
            return [str(v).strip() for v in value if v is not None and str(v).strip() != "" and str(v).strip() != "null"]

        if isinstance(value, str):
            # Split by common delimiters and clean up
            import re
            # Split by comma, semicolon, newline, or pipe
            items = re.split(r'[,;\n|]', value)
            return [item.strip() for item in items if item.strip() != "" and item.strip() != "null"]

        # For other types, convert to string
        return [str(value).strip()] if str(value).strip() != "" and str(value).strip() != "null" else []

    def _list_to_string(self, value_list: list[str]) -> str:
        """
        Convert list of strings back to a delimited string for database storage

        Args:
            value_list: List of strings

        Returns:
            Comma-separated string
        """
        if not value_list:
            return None

        # Remove duplicates while preserving order
        unique_values = []
        for item in value_list:
            if item not in unique_values:
                unique_values.append(item)

        return ", ".join(unique_values) if unique_values else None

    def _process_text_for_openai(self, text_content: str) -> str:
        """
        Process text content according to diagram specifications:
        - Split text by spaces
        - If len(words) < 80k: pass as-is
        - If len(words) > 80k: take first 40k + last 40k words
        - Convert back to text
        """
        try:
            # Split text into words by space
            words = text_content.split()
            word_count = len(words)

            self.logger.info(f"Processing text with {word_count} words for OpenAI")

            if word_count <= 80000:
                # Text is within limit, pass as-is
                self.logger.info("Text within 80k word limit, using full text")
                return text_content
            else:
                # Text exceeds limit, take first 40k + last 40k words
                self.logger.info("Text exceeds 80k words, taking first 40k + last 40k words")
                first_40k = words[:40000]
                last_40k = words[-40000:]

                # Combine and convert back to text
                processed_words = first_40k + last_40k
                processed_text = ' '.join(processed_words)

                self.logger.info(f"Processed text: {len(processed_words)} words total")
                return processed_text

        except Exception as e:
            self.logger.error(f"Error processing text for OpenAI: {str(e)}")
            # Fallback: return first 8000 characters
            return text_content[:8000]

 
    def _store_analysis_results(
        self,
        request: EntityExtractionRequest,
        merged_result: Dict[str, Any],
        reachable_urls: list,
        unreachable_urls: list,
        filtered_policy_urls: Dict[str, Dict[str, Any]],
        extraction_method: str
    ) -> int:
        """
        Store entity extraction results in the database

        Returns:
            analysis_id: The ID of the created analysis record
        """
        try:
            with Session(engine) as session:
                # Convert string values to boolean for database storage (database expects boolean)
                accepts_international = merged_result.get("accepts_international_orders")
                if isinstance(accepts_international, str):
                    accepts_international = accepts_international.lower() == "yes" if accepts_international.lower() in ["yes", "no"] else None

                has_jurisdiction = merged_result.get("has_jurisdiction_law")
                if isinstance(has_jurisdiction, str):
                    has_jurisdiction = has_jurisdiction.lower() == "yes" if has_jurisdiction.lower() in ["yes", "no"] else None

                # Convert list fields to strings for database storage
                list_fields = ["business_email", "support_email", "business_contact_numbers", "business_location", "shipping_countries"]
                db_values = {}
                for field in list_fields:
                    value = merged_result.get(field)
                    if isinstance(value, list):
                        db_values[field] = self._list_to_string(value)
                    else:
                        db_values[field] = value

                # Debug logging for jurisdiction fields
                self.logger.info(f"Before database storage - jurisdiction_details: {merged_result.get('jurisdiction_details')}")
                self.logger.info(f"Before database storage - jurisdiction_place: {merged_result.get('jurisdiction_place')}")
                self.logger.info(f"Before database storage - has_jurisdiction_law: {has_jurisdiction}")

                # Prepare URL data for storage
                all_urls = [v.get('url') or v.get('original_url') for v in filtered_policy_urls.values() if v.get('url') or v.get('original_url')]

                # Create analysis record
                analysis = EntityExtractionAnalysis(
                    scrape_request_ref_id=request.scrape_request_ref_id,
                    website_url=request.website_url,
                    processing_status="COMPLETED",
                    legal_name=merged_result.get("legal_name"),
                    business_email=merged_result.get("business_email"),
                    support_email=merged_result.get("support_email"),
                    business_contact_numbers=merged_result.get("business_contact_numbers"),
                    business_location=merged_result.get("business_location"),
                    accepts_international_orders=accepts_international,
                    shipping_policy_details=merged_result.get("shipping_policy_details"),
                    has_jurisdiction_law=has_jurisdiction,
                    jurisdiction_details=merged_result.get("jurisdiction_details"),
                    jurisdiction_place=merged_result.get('jurisdiction_place'),
                    shipping_countries=merged_result.get('shipping_countries') if isinstance(merged_result.get('shipping_countries'), str) else ", ".join(merged_result.get('shipping_countries') or []),
                    urls_reachable_by_gemini=json.dumps(reachable_urls) if reachable_urls else None,
                    urls_not_reachable_by_gemini=json.dumps(unreachable_urls) if unreachable_urls else None,
                    extraction_method=extraction_method,
                    total_urls_processed=len(all_urls),
                    org_id=request.org_id,
                    started_at=datetime.now().isoformat(),
                    created_at=datetime.now().isoformat()
                )

                session.add(analysis)
                session.commit()
                session.refresh(analysis)

                self.logger.info(f"Stored entity extraction analysis with ID: {analysis.id}")
                return analysis.id

        except Exception as e:
            self.logger.error(f"Error storing analysis results: {str(e)}")
            return 0

    def _store_failed_analysis(self, request: EntityExtractionRequest, error_message: str) -> int:
        """
        Store failed entity extraction analysis in the database

        Returns:
            analysis_id: The ID of the created analysis record
        """
        try:
            with Session(engine) as session:
                analysis = EntityExtractionAnalysis(
                    scrape_request_ref_id=request.scrape_request_ref_id,
                    website_url=request.website_url,
                    processing_status="FAILED",
                    error_message=error_message,
                    org_id=request.org_id,
                    started_at=datetime.now().isoformat(),
                    created_at=datetime.now().isoformat()
                )

                session.add(analysis)
                session.commit()
                session.refresh(analysis)

                self.logger.info(f"Stored failed entity extraction analysis with ID: {analysis.id}")
                return analysis.id

        except Exception as e:
            self.logger.error(f"Error storing failed analysis: {str(e)}")
            return 0

    def _get_existing_analysis(self, scrape_request_ref_id: str, org_id: str) -> Optional[EntityExtractionAnalysis]:
        """Get existing analysis if it exists - returns the latest one by ID"""
        try:
            with next(get_session()) as session:
                from sqlmodel import select
                query = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.scrape_request_ref_id == scrape_request_ref_id,
                    EntityExtractionAnalysis.org_id == org_id
                ).order_by(EntityExtractionAnalysis.id.desc())  # Get the latest record first

                result = session.exec(query).first()
                if result:
                    self.logger.info(f"Found existing analysis with ID: {result.id}, status: {result.processing_status}")
                else:
                    self.logger.info("No existing analysis found")
                return result
        except Exception as e:
            self.logger.error("Error checking for existing analysis", error=e)
            return None
    
    def _create_analysis_record(self, request: EntityExtractionRequest) -> EntityExtractionAnalysis:
        """Create new analysis record with duplicate check"""
        try:
            with next(get_session()) as session:
                # Double-check for existing record before creating
                from sqlmodel import select
                existing_check = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.scrape_request_ref_id == request.scrape_request_ref_id,
                    EntityExtractionAnalysis.org_id == request.org_id
                ).order_by(EntityExtractionAnalysis.id.desc())

                existing = session.exec(existing_check).first()
                if existing:
                    self.logger.warning(f"Found existing analysis during create - returning existing ID: {existing.id}")
                    return existing

                # Create new record only if none exists
                analysis = EntityExtractionAnalysis(
                    scrape_request_ref_id=request.scrape_request_ref_id,
                    website_url=request.website_url,
                    processing_status="PENDING",
                    org_id=request.org_id
                )
                session.add(analysis)
                session.commit()
                session.refresh(analysis)

                self.logger.info(f"Created new analysis record with ID: {analysis.id}")
                return analysis
                
        except Exception as e:
            self.logger.error("Error creating analysis record", error=e)
            raise
    
    def _update_analysis_status(self, analysis_id: int, status: str, 
                              started_at: Optional[str] = None,
                              completed_at: Optional[str] = None,
                              error_message: Optional[str] = None):
        """Update analysis status"""
        try:
            with next(get_session()) as session:
                analysis = session.get(EntityExtractionAnalysis, analysis_id)
                if analysis:
                    analysis.processing_status = status
                    if started_at:
                        analysis.started_at = started_at
                    if completed_at:
                        analysis.completed_at = completed_at
                    if error_message:
                        analysis.error_message = error_message
                    
                    session.commit()
                    self.logger.info(f"Updated analysis {analysis_id} status to {status}")
                    
        except Exception as e:
            self.logger.error(f"Error updating analysis status", error=e)
    
    def _update_analysis_with_results(self, analysis_id: int, 
                                    entity_results: Dict[str, Any],
                                    terms_results: Dict[str, Any],
                                    shipping_results: Dict[str, Any],
                                    reachable_urls: list,
                                    unreachable_urls: list):
        """Update analysis with extraction results"""
        try:
            with next(get_session()) as session:
                analysis = session.get(EntityExtractionAnalysis, analysis_id)
                if analysis:
                    # Update entity extraction results
                    analysis.legal_name = entity_results.get("legal_name")
                    analysis.business_email = entity_results.get("business_email")
                    analysis.support_email = entity_results.get("support_email")
                    analysis.business_contact_numbers = entity_results.get("business_contact_numbers")
                    analysis.business_location = entity_results.get("business_location")
                    
                    # Update Terms & Conditions results
                    analysis.has_jurisdiction_law = terms_results.get("has_jurisdiction_law")
                    analysis.jurisdiction_details = terms_results.get("jurisdiction_details")
                    
                    # Update Shipping Policy results
                    analysis.accepts_international_orders = shipping_results.get("accepts_international_orders")
                    # Handle both possible field names for shipping details
                    shipping_details = (shipping_results.get("shipping_policy_details") or
                                      shipping_results.get("shipping_details"))
                    analysis.shipping_policy_details = shipping_details
                    
                    # Update URL reachability
                    analysis.urls_reachable_by_gemini = json.dumps(reachable_urls)
                    analysis.urls_not_reachable_by_gemini = json.dumps(unreachable_urls)
                    
                    # Determine extraction method
                    methods = [
                        entity_results.get("extraction_method", ""),
                        terms_results.get("analysis_method", ""),
                        shipping_results.get("analysis_method", "")
                    ]
                    if "gemini" in methods and "openai" in methods:
                        analysis.extraction_method = "mixed"
                    elif "gemini" in methods:
                        analysis.extraction_method = "gemini"
                    elif "openai" in methods:
                        analysis.extraction_method = "openai"
                    else:
                        analysis.extraction_method = "failed"
                    
                    session.commit()
                    self.logger.info(f"Updated analysis {analysis_id} with extraction results")
                    
        except Exception as e:
            self.logger.error(f"Error updating analysis with results", error=e)
    
    def _get_analysis_by_id(self, analysis_id: int) -> Optional[EntityExtractionAnalysis]:
        """Get analysis by ID"""
        try:
            with next(get_session()) as session:
                return session.get(EntityExtractionAnalysis, analysis_id)
        except Exception as e:
            self.logger.error(f"Error getting analysis by ID {analysis_id}", error=e)
            return None
    
    def _convert_to_response(self, analysis: EntityExtractionAnalysis) -> EntityExtractionResponse:
        """Convert analysis record to response model"""
        try:
            # Parse URL lists
            urls_processed = []
            if analysis.urls_reachable_by_gemini:
                try:
                    urls_processed.extend(json.loads(analysis.urls_reachable_by_gemini))
                except:
                    pass
            if analysis.urls_not_reachable_by_gemini:
                try:
                    urls_processed.extend(json.loads(analysis.urls_not_reachable_by_gemini))
                except:
                    pass

            # Convert boolean values from database to string values for response
            def bool_to_string(value):
                if value is True:
                    return "yes"
                elif value is False:
                    return "no"
                else:
                    return "unclear"

            return EntityExtractionResponse(
                analysis_id=analysis.id,
                scrape_request_ref_id=analysis.scrape_request_ref_id,
                website_url=analysis.website_url,
                processing_status=analysis.processing_status,
                legal_name=analysis.legal_name,
                business_email=analysis.business_email,
                support_email=analysis.support_email,
                business_contact_numbers=analysis.business_contact_numbers,
                business_location=analysis.business_location,
                has_jurisdiction_law=bool_to_string(analysis.has_jurisdiction_law),
                jurisdiction_details=analysis.jurisdiction_details,
                accepts_international_orders=bool_to_string(analysis.accepts_international_orders),
                shipping_policy_details=analysis.shipping_policy_details,
                jurisdiction_place=analysis.jurisdiction_place,
                shipping_countries=analysis.shipping_countries,
                privacy_policy_text=analysis.privacy_policy_text,
                terms_conditions_text=analysis.terms_conditions_text,
                extraction_method=analysis.extraction_method,
                urls_processed=urls_processed if urls_processed else None,
                error_message=analysis.error_message,
                created_at=analysis.created_at,
                completed_at=analysis.completed_at
            )
            
        except Exception as e:
            self.logger.error("Error converting analysis to response", error=e)
            # Return minimal response on error
            return EntityExtractionResponse(
                analysis_id=analysis.id if analysis else 0,
                scrape_request_ref_id=analysis.scrape_request_ref_id if analysis else "",
                website_url=analysis.website_url if analysis else "",
                processing_status="FAILED",
                error_message=f"Response conversion error: {str(e)}",
                created_at=analysis.created_at if analysis else datetime.now().isoformat()
            )

    def _get_policy_texts_from_db(self, scrape_request_ref_id: str) -> Dict[str, str]:
        """
        Get all available policy texts from policy_analysis_new_gemini table

        Args:
            scrape_request_ref_id: The scrape request reference ID

        Returns:
            Dictionary mapping policy types to their extracted text content
        """
        try:
            from app.models.db_models import PolicyAnalysisNew
            from app.database import get_session
            from sqlmodel import select

            with next(get_session()) as session:
                query = select(PolicyAnalysisNew).where(
                    PolicyAnalysisNew.scrape_request_ref_id == scrape_request_ref_id
                ).order_by(PolicyAnalysisNew.id.desc())

                policy_record = session.exec(query).first()

                if not policy_record:
                    self.logger.warning(f"No policy analysis record found for {scrape_request_ref_id}")
                    return {}

                # Extract all available text fields
                policy_texts = {}
                text_fields = {
                    'privacy_policy_text': 'privacy_policy',
                    'terms_and_condition_text': 'terms_and_condition',
                    'shipping_delivery_text': 'shipping_delivery',
                    'returns_cancellation_exchange_text': 'returns_cancellation_exchange',
                    'contact_us_text': 'contact_us',
                    'about_us_text': 'about_us',
                    'home_page_text': 'home_page'
                }

                for db_field, policy_type in text_fields.items():
                    text_content = getattr(policy_record, db_field, None)
                    if text_content and text_content.strip() and text_content != 'not_applicable':
                        # Limit text size for OpenAI processing
                        if len(text_content) > 15000:
                            text_content = text_content[:7500] + "\n...\n" + text_content[-7500:]
                        policy_texts[policy_type] = text_content

                self.logger.info(f"Retrieved policy texts: {list(policy_texts.keys())}")
                return policy_texts

        except Exception as e:
            self.logger.error(f"Error retrieving policy texts from database: {str(e)}")
            return {}

    def _process_missing_fields_with_openai(self, missing_fields: list, policy_texts: Dict[str, str], scrape_request_ref_id: str) -> Dict[str, Any]:
        """
        Process missing fields using multiple focused OpenAI calls with token limits

        Args:
            missing_fields: List of field names that need to be extracted
            policy_texts: Dictionary of policy type to extracted text content
            scrape_request_ref_id: Request ID for logging

        Returns:
            Dictionary with extracted field values
        """
        try:
            from app.gpt_models.chatgpt_utils import call_gpt4o_mini
            import tiktoken

            # Initialize tokenizer for GPT-4
            encoding = tiktoken.encoding_for_model("gpt-4")

            # Group fields by their likely policy sources for focused extraction
            field_groups = {
                "contact_info": {
                    "fields": ["business_email", "support_email", "business_contact_numbers", "business_location"],
                    "policy_types": ["contact_us", "about_us", "home_page"],
                    "description": "contact information, emails, phone numbers, and business addresses"
                },
                "legal_info": {
                    "fields": ["legal_name", "has_jurisdiction_law", "jurisdiction_details", "jurisdiction_place"],
                    "policy_types": ["terms_and_condition", "privacy_policy", "about_us"],
                    "description": "legal company name, jurisdiction, and governing law information"
                },
                "shipping_info": {
                    "fields": ["accepts_international_orders", "shipping_countries", "shipping_policy_details"],
                    "policy_types": ["shipping_delivery", "returns_cancellation_exchange", "terms_and_condition"],
                    "description": "shipping policies, international orders, and delivery information"
                }
            }

            all_extracted_data = {}

            # Process each field group with focused OpenAI calls
            for group_name, group_config in field_groups.items():
                # Check if any fields from this group are missing
                group_missing_fields = [f for f in missing_fields if f in group_config["fields"]]
                if not group_missing_fields:
                    continue

                self.logger.info(f"Processing {group_name} with fields: {group_missing_fields}")

                # Get relevant policy texts for this group
                relevant_texts = {}
                for policy_type in group_config["policy_types"]:
                    if policy_type in policy_texts:
                        relevant_texts[policy_type] = policy_texts[policy_type]

                if not relevant_texts:
                    self.logger.warning(f"No relevant policy texts found for {group_name}")
                    continue

                # Process texts with token limit (~90K tokens = ~70K characters)
                extracted_data = self._make_focused_openai_call(
                    group_missing_fields,
                    relevant_texts,
                    group_config["description"],
                    scrape_request_ref_id,
                    group_name,
                    encoding,
                    max_tokens=90000
                )

                all_extracted_data.update(extracted_data)

            # If we still have missing fields, try a final comprehensive call
            remaining_fields = [f for f in missing_fields if f not in all_extracted_data]
            if remaining_fields:
                self.logger.info(f"Making final comprehensive call for remaining fields: {remaining_fields}")

                # Use all available policy texts for remaining fields
                final_extracted = self._make_focused_openai_call(
                    remaining_fields,
                    policy_texts,
                    "any business information",
                    scrape_request_ref_id,
                    "comprehensive",
                    encoding,
                    max_tokens=90000
                )

                all_extracted_data.update(final_extracted)

            self.logger.info(f"Total OpenAI backup extracted {len(all_extracted_data)} fields: {list(all_extracted_data.keys())}")
            return all_extracted_data

        except Exception as e:
            self.logger.error(f"Error in OpenAI backup processing: {str(e)}")
            return {}

    def _make_focused_openai_call(self, target_fields: list, policy_texts: Dict[str, str],
                                 description: str, scrape_request_ref_id: str, group_name: str,
                                 encoding, max_tokens: int = 90000) -> Dict[str, Any]:
        """
        Make a focused OpenAI call for specific fields with token limit

        Args:
            target_fields: List of specific fields to extract
            policy_texts: Dictionary of policy texts
            description: Description of what to extract
            scrape_request_ref_id: Request ID for logging
            group_name: Name of the field group for logging
            encoding: Tokenizer encoding
            max_tokens: Maximum tokens to use

        Returns:
            Dictionary with extracted field values
        """
        try:
            from app.gpt_models.chatgpt_utils import call_gpt4o_mini
            import json
            import re

            # Prepare policy texts with token management
            combined_text = ""
            total_tokens = 0

            for policy_type, text_content in policy_texts.items():
                # Calculate tokens for this policy text
                policy_header = f"\n\n=== {policy_type.upper()} POLICY ===\n"
                policy_tokens = len(encoding.encode(policy_header + text_content))

                # Check if adding this policy would exceed token limit
                if total_tokens + policy_tokens > max_tokens * 0.8:  # Leave 20% for prompt
                    # Truncate the text to fit
                    remaining_tokens = int((max_tokens * 0.8) - total_tokens)
                    if remaining_tokens > 1000:  # Only add if we have meaningful space
                        truncated_text = encoding.decode(encoding.encode(text_content)[:remaining_tokens])
                        combined_text += policy_header + truncated_text + "\n[TRUNCATED]"
                        total_tokens += remaining_tokens
                    break
                else:
                    combined_text += policy_header + text_content
                    total_tokens += policy_tokens

            if not combined_text.strip():
                self.logger.warning(f"No policy text available for {group_name} extraction")
                return {}

            # Create focused prompt for specific fields
            target_fields_str = ", ".join(target_fields)

            prompt = f"""
You are an expert at extracting business information from policy documents and website content.

TASK: Extract ONLY these specific fields related to {description}: {target_fields_str}

POLICY TEXTS:
{combined_text}

EXTRACTION RULES:
1. Extract ONLY the requested fields: {target_fields_str}
2. For email fields: Look for business/support email addresses (avoid noreply@, info@ unless clearly business)
3. For contact numbers: Extract phone numbers with country codes, format cleanly
4. For business location: Extract complete physical business address or headquarters
5. For legal name: Extract the official registered company/business name
6. For jurisdiction: Look for "governed by", "subject to laws of", dispute resolution clauses
7. For shipping: Look for international shipping, delivery policies, supported countries
8. Be thorough - check all sections for relevant information
9. Return "null" ONLY if information is genuinely not found after thorough search
10. For yes/no fields, return exactly "yes", "no", or "unclear"

RESPONSE FORMAT (JSON only):
{{"""

            # Add only the target fields to the JSON template
            json_fields = []
            for field in target_fields:
                if field == "legal_name":
                    json_fields.append(f'    "{field}": "extracted legal company name or null"')
                elif field == "business_email":
                    json_fields.append(f'    "{field}": "extracted business email or null"')
                elif field == "support_email":
                    json_fields.append(f'    "{field}": "extracted support/customer email or null"')
                elif field == "business_contact_numbers":
                    json_fields.append(f'    "{field}": "extracted phone numbers or null"')
                elif field == "business_location":
                    json_fields.append(f'    "{field}": "extracted business address or null"')
                elif field == "has_jurisdiction_law":
                    json_fields.append(f'    "{field}": "yes/no/unclear"')
                elif field == "jurisdiction_details":
                    json_fields.append(f'    "{field}": "extracted jurisdiction details or null"')
                elif field == "jurisdiction_place":
                    json_fields.append(f'    "{field}": "extracted jurisdiction location or null"')
                elif field == "accepts_international_orders":
                    json_fields.append(f'    "{field}": "yes/no/unclear"')
                elif field == "shipping_countries":
                    json_fields.append(f'    "{field}": "extracted shipping countries or null"')
                elif field == "shipping_policy_details":
                    json_fields.append(f'    "{field}": "extracted shipping policy details or null"')

            prompt += "\n" + ",\n".join(json_fields) + "\n}\n\nReturn only the JSON response, no additional text."

            # Make OpenAI API call
            messages = [{"role": "user", "content": prompt}]
            openai_response_obj = call_gpt4o_mini(messages, name=f"entity_extraction_{group_name}")

            # Log OpenAI API response
            api_logger.log_openai_response(
                messages=messages,
                response=openai_response_obj,
                name=f"entity_extraction_{group_name}",
                request_id=scrape_request_ref_id,
                context={"service": f"orchestrator_{group_name}", "target_fields": target_fields, "tokens_used": total_tokens}
            )

            if not openai_response_obj:
                self.logger.warning(f"No response from OpenAI {group_name} call")
                return {}

            raw_content = openai_response_obj.choices[0].message.content

            # Parse JSON response
            json_text_match = re.search(r"```json\s*(\{.*?\})\s*```", raw_content, re.DOTALL)
            if json_text_match:
                json_text = json_text_match.group(1)
            else:
                json_text = raw_content.strip()

            try:
                parsed_result = json.loads(json_text)

                # Filter to only include non-null values for target fields
                filtered_result = {}
                for field in target_fields:
                    if field in parsed_result and parsed_result[field] not in ["null", None, ""]:
                        filtered_result[field] = parsed_result[field]

                self.logger.info(f"OpenAI {group_name} call extracted {len(filtered_result)} fields: {list(filtered_result.keys())}")
                return filtered_result

            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse OpenAI {group_name} response as JSON: {str(e)}")
                self.logger.error(f"Raw response: {raw_content[:300]}...")
                return {}

        except Exception as e:
            self.logger.error(f"Error in OpenAI {group_name} call: {str(e)}")
            return {}
