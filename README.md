# Entity Extraction API

A FastAPI-based service for extracting business entities from website content. This system analyzes website URLs and extracts key business information such as legal names, contact details, shipping policies, and jurisdiction information using advanced AI models (OpenAI GPT and Google Gemini).

## Features

- **Advanced Entity Extraction**: Extract business entities from website content
- **Shipping Policy Analysis**: Analyze shipping policies and international shipping capabilities
- **Terms and Conditions Analysis**: Extract jurisdiction and legal information
- **URL Data Retrieval and Processing**: Intelligent URL classification and content retrieval
- **Multi-Model AI Support**: Uses both OpenAI GPT and Google Gemini models with intelligent fallback mechanisms
- **Combined Extraction Method**: Optimized approach using Gemini first, then OpenAI to fill missing fields
- **Comprehensive Database Integration**: Stores analysis results and tracks processing status
- **Automatic Token Management**: Ensures AI API calls stay within token limits
- **Reachability Detection**: Automatically detects which URLs are accessible by different AI models

## Setup

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Set up environment variables:
   ```bash
   # Required API keys
   export OPENAI_API_KEY="your-openai-api-key"
   export GEMINI_API_KEY="your-gemini-api-key"

   # Database configuration
   export DATABASE_URL="your-database-connection-string"
   ```

## Database Initialization

The database tables are automatically created when the application starts. You can also manually create them:

```python
from app.Extractor.database_setup import create_entity_extractor_tables
create_entity_extractor_tables()
```

## Usage

### API Endpoints

Start the FastAPI server:
```bash
uvicorn app.main:app --reload
```

The API will be available at `http://localhost:8000` with the following endpoints:

#### Entity Extraction Analysis
```bash
# Full entity extraction with scrape request reference ID
POST /entity-extraction/analyze
{
    "scrape_request_ref_id": "req_123",
    "website_url": "https://example.com",
    "org_id": "default",
    "force_reprocess": false,
    "use_openai_fallback": true
}

# Combined entity extraction (Gemini first, then OpenAI for missing fields)
POST /entity-extraction/analyze-combined
{
    "website_url": "https://example.com",
    "org_id": "default",
    "force_reprocess": false,
    "use_openai_fallback": true
}

# Simplified entity extraction (finds scrape request automatically)
POST /entity-extraction/analyze-simple
{
    "website_url": "https://example.com",
    "org_id": "default",
    "force_reprocess": false,
    "use_openai_fallback": true
}
```

#### Combined Entity Extraction Method

The `/analyze-combined` endpoint uses an optimized approach:

1. **Gemini First**: Makes initial extraction call using Gemini API
2. **Null Detection**: Analyzes Gemini results for missing or null values
3. **OpenAI Fallback**: Uses OpenAI to fill only the missing fields
4. **Intelligent Merging**: Combines results with field source tracking

**Response includes:**
- `gemini_result`: Raw results from Gemini API
- `openai_result`: Results from OpenAI for missing fields
- `combined_result`: Final merged results
- `field_sources`: Shows which API provided each field
- `missing_fields_count`: Number of fields Gemini couldn't extract
- `total_fields_extracted`: Total successful extractions

**Benefits:**
- Maximizes accuracy by using both AI models
- Reduces API costs by only calling OpenAI for missing fields
- Provides transparency about data sources
- Handles various null value representations

### Direct Service Usage

```python
from app.Extractor.models.request_models import EntityExtractionRequest
from app.Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator

# Create a request
request = EntityExtractionRequest(
    scrape_request_ref_id="req_example_123",
    website_url="https://example.com",
    org_id="default",
    force_reprocess=False,
    use_openai_fallback=True
)

# Process entity extraction
orchestrator = EntityExtractorOrchestrator()
result = orchestrator.process_entity_extraction(request)

print(f"Analysis ID: {result.analysis_id}")
print(f"Legal Name: {result.legal_name}")
print(f"Business Email: {result.business_email}")
print(f"Shipping Countries: {result.shipping_countries}")
```

## Entity Extraction Process

The system follows a sophisticated workflow for extracting business entities:

1. **URL Data Retrieval**: Retrieves classified URLs from the database
2. **Policy URL Identification**: Identifies privacy policies, terms of service, and shipping policies
3. **Reachability Testing**: Tests which URLs are accessible by Gemini and OpenAI models
4. **Primary Extraction (Gemini)**: Uses Gemini for bulk entity extraction from reachable URLs
5. **Fallback Extraction (OpenAI)**: Uses OpenAI for individual URL processing when Gemini fails
6. **Result Merging**: Combines results from both models for comprehensive entity extraction
7. **Database Storage**: Stores extracted entities and analysis metadata

## Extracted Entities

The system extracts the following business information:

### Contact Information
- `legal_name`: Official business name
- `business_email`: Primary business email address
- `support_email`: Customer support email
- `business_contact_numbers`: Phone numbers and contact details
- `business_location`: Business address and location information

### Policy Information
- `has_jurisdiction_law`: Whether jurisdiction laws are mentioned
- `jurisdiction_details`: Specific jurisdiction information
- `jurisdiction_place`: Location of legal jurisdiction
- `accepts_international_orders`: International shipping capability
- `shipping_policy_details`: Detailed shipping policy information
- `shipping_countries`: List of countries shipped to

### Content Analysis
- `privacy_policy_text`: Extracted privacy policy content
- `terms_conditions_text`: Extracted terms and conditions content

## API Response Example

```json
{
    "analysis_id": 123,
    "scrape_request_ref_id": "req_example_123",
    "website_url": "https://example.com",
    "processing_status": "COMPLETED",
    "legal_name": "Example Business LLC",
    "business_email": "<EMAIL>",
    "support_email": "<EMAIL>",
    "business_contact_numbers": "******-0123",
    "business_location": "123 Main St, City, State 12345",
    "accepts_international_orders": "yes",
    "shipping_countries": "United States, Canada, United Kingdom",
    "has_jurisdiction_law": "yes",
    "jurisdiction_place": "State of Delaware"
}
```

## Testing

Test the Entity Extraction functionality:

```bash
# Test the API endpoints
curl -X POST "http://localhost:8000/entity-extraction/analyze-simple" \
     -H "Content-Type: application/json" \
     -d '{"website_url": "https://example.com"}'

# Check health endpoint
curl http://localhost:8000/health
```

## Environment Variables

Required environment variables:

- `OPENAI_API_KEY`: Your OpenAI API key for GPT models
- `GEMINI_API_KEY`: Your Google Gemini API key
- `DATABASE_URL`: Database connection string (MySQL or SQLite)

## Architecture

The Entity Extraction system is built with:

- **FastAPI**: Modern web framework for building APIs
- **SQLModel**: Type-safe database ORM
- **OpenAI GPT**: Advanced language model for entity extraction
- **Google Gemini**: Alternative AI model with URL access capabilities
- **Playwright**: Web scraping for content retrieval
- **Comprehensive Logging**: Detailed operation tracking and debugging
